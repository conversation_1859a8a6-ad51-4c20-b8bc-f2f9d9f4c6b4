<template>
  <div class="maskbg">
    <div class="titlediv">
      <div class="title">数据管理</div>
    </div>
    <el-form class="myform" label-width="80px">
        <el-form-item label="知识点:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.num"
            placeholder="请输入知识点数量"
            />
        </el-form-item>
        <el-form-item label="知识图谱:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.num1"
            placeholder="请输入知识图谱数量"
            />
        </el-form-item>
        <el-form-item label="能力图片:">
            <el-input-number 
            class="definput"
            :controls="false" 
            v-model="form.num2"
            placeholder="请输入能力图片数量"
            />
        </el-form-item>
        <el-form-item>
            <el-button class="defbtn pd">立即保存</el-button>
        </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive } from "vue";

let form = reactive({
    num:undefined,
    num1:undefined,
    num2:undefined
})
</script>

<style lang="scss" scoped>
.myform{
    margin-top: 20px;
}
.titlediv{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}
.definput{
  width: 280px;
  height: 36px;
  border-radius: 4px;
  ::v-deep(.el-input__inner){
    text-align: left !important;
  }
}
.pd{
    margin-top: 20px;
    padding: 9px 48px !important;
}
</style>