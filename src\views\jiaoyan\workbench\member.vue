<template>
    <el-tabs 
    class="mytabs" 
    v-model="activeName" 
    @tab-click="handleClick"
    :style="{
        '--el-border-color-light':'#D9D9D9'
    }">
        <el-tab-pane label="人员审核" name="first">
            
        </el-tab-pane>
        <el-tab-pane label="全部人员" name="second">
            
        </el-tab-pane>
        <member-table  ref="table1" v-if="activeName == 'first'" :type="1"></member-table>
        <member-table ref="table2" v-if="activeName == 'second'" :type="2"></member-table>
    </el-tabs>
</template>

<script setup>
import memberTable from './common/memberTable.vue';
const activeName = ref('first')
const table1 =  ref()
const table2 = ref()
const handleClick = (type) =>{
    console.log(type);
}
</script>

<style lang="scss" scoped>
.mytabs{
    ::v-deep(.el-tabs__nav){
        padding: 20px 30px 8px;
    }
    ::v-deep(.el-tabs__item){
        font-family: Source <PERSON>, Source <PERSON>;
        font-weight: 400;
        font-size: 20px;
        color: #44474D;
        text-align: left;
        font-style: normal;
        text-transform: none;
        &.is-active{
            color: #386CFC !important;
        }
    }
    ::v-deep(.el-tabs__content){
        padding: 10px 20px;
    }
}

</style>