<template>
    <el-dialog 
    width="600px" 
    class="adddialog"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
    title="开始直播" 
    v-model="dialogVisible" 
    @close="close" 
    :close-on-click-modal="false">
        
        <el-form 
        ref="addform" 
        label-width="100px"
        label-position="top">
            <el-form-item label-width="0px">
                <div class="tdivcss">
                    <span class="title">推流地址</span>
                    <span class="msgcss">在OBS软件中输入以上URL与流名称</span>
                </div>
            </el-form-item>
            <el-form-item label="URL" prop="name">
                <div class="urldiv">
                    <el-text tag="div" truncated class="urlcss">rtmp://push.maisuiedu.com/wthlive</el-text>
                    <el-button class="linebtn" @click="copyStr('rtmp://push.maisuiedu.com/wthlive')">复制</el-button>
                </div>
            </el-form-item>
            <el-form-item label="流名称" prop="coverUrl">
                <div class="urldiv">
                    <el-text tag="div" truncated class="urlcss">liveStream1763106194089689657key=1753416163-0-0-010016</el-text>
                    <el-button class="linebtn" @click="copyStr('liveStream1763106194089689657key=1753416163-0-0-010016')">复制</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script setup>
import { 
    getMyBookList, 
    saveMaterial, 
    getMaterialDetail, 
    updateMaterial, 
    deleteMaterial } from '@/api/expert/index.js'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const dialogVisible = ref(false)
const addform = ref()
const show = () =>{
    dialogVisible.value = true;
}
function close() {
    dialogVisible.value = false
}
function copyStr(link) {
    const input = document.createElement("input");
    document.body.appendChild(input);
    input.setAttribute("value", link);
    input.select();
    document.execCommand("Copy");
    document.body.removeChild(input);
    ElMessage.success("复制成功");
}
onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.urldiv{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .urlcss{
        width:calc(100% - 110px);
        height: 36px;
        line-height: 36px;
        background: #FFFFFF;
        border-radius: 4px;
        padding: 0 16px;
        border: 1px solid #E1E4EB;
    }
    .linebtn{
        height: 36px !important;
        background-color: #EBF0FF !important;
    }
}
.tdivcss{
    display: flex;
    align-items: center;
    .title{
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #2D2F33;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .msgcss{
        margin-left: 12px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #878D99;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>

<style lang="scss">

</style>