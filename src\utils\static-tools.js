export const resourceTypes = [
  { id: "", name: '全部' },
  { id: 1, name: "文档" },
  { id: 2, name: "视频" },
  { id: 3, name: "音频" },
  { id: 4, name: "图片" },
]
export const resourceTypesMap = {
  1: '文档',
  2: '视频',
  3: '音频',
  4: '图片'
}

export const fTypes = [
  { id: 1, name: '教学资源' },
  { id: 2, name: '教学素材' }
]
export const fTypesMap = {
  1: '教学资源',
  2: '教学素材'
}

export const fTypes1 = [
  { id: 1, name: '新闻公告' },
  { id: 2, name: '教学文件' }
]

// 教研室资源类型
export const ResourceTypes = [
  { id: 1, name: "数字教材" },
  { id: 2, name: "微课" },
  { id: 3, name: "教学课件" },
  { id: 5, name: "教学设计" },
  { id: 6, name: "教学视频" },
  { id: 7, name: "会议录像" }
]

export const ResourceTypesMap = ResourceTypes.reduce((map, item) => {
  map[item.id] = item.name;
  return map;
}, {});

// 资源标签
export const resourceTags = [
  { id: "", name: '全部' },
  { id: 1, name: '教学资源' },
  { id: 2, name: '教研成果' },
  { id: 3, name: '教研资料' }
]

export const resourceTagsMap = {
  1: '教学资源',
  2: '教研成果',
  3: '教研资料'
}

export const activityTypes = [
  {
    id:1, name:'线上教研'
  },
  {
    id:2, name:'线下教研'
  },
]

export const activityTypesMap = {
  1:'线上教研',
  2:'线下教研'
}

export const activityStatusMap = {
  1:'直播中',
  2:'未开始',
  3:'已结束'
}
export const activityStatusColorMap = {
  1:'c1',
  2:'c2',
  3:'c3'
}