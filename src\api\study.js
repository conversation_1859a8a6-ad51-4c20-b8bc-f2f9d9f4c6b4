import service from '@/utils/request.js'
// 课程列表（查询createBy创建的列表）
export function queryCourseList(params) {
	return service.request({
		method: 'get',
		url: `/digital/controller/course/list`,
		params
	});
}
// 新建课程
export function courseSave(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/course/save`,
		data
	})
}
// 课程编辑
export function courseUpdate(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/course/update`,
		data
	})
}
// 置顶
export function courseoperateSave(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseoperate/save`,
		data
	})
}

// 取消置顶
export function courseoperateDelete(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseoperate/delete`,
		data
	})
}
export function courseDelete(courseId) {
	return service.request({
		method: 'post',
		url:`/digital/controller/course/delete/${courseId}`,
	})
}
// 课程详情
export function courseInfo(courseId) {
	return service.request({
		method: 'get',
		url:`/digital/controller/course/info/${courseId}`,
	})
}
// 课程学生列表
export function queryCourseUserList(params) {
	return service.request({
		method: 'get',
		url: `/digital/controller/courseuser/list`,
		params
	});
}

// 保存课程关联学生
export function courseUserSave(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseuser/save`,
		data
	})
}
// 根据课程码查询课程信息

export function queryCourseVoByParam(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/course/queryCourseVoByParam`,
		params
	})
}


export function courseUserDelete(stuId) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseuser/delete/${stuId}`,
	})
}

// 
export function courseResourceList(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/courseresource/list`,
		params
	})
}

// 课程保存教材
export function courseResourceSave(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseresource/save`,
		data
	})
}

// 根据ID删除课程教材
export function courseResourceDelete(id) {
	return service.request({
		method: 'post',
		url:`/digital/controller/courseresource/delete/${id}`,
	})
}
// 课程选择教材列表、出版社教材列表、首页教材、教材库教材
export function departmentRepositoryList(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/departmentRepository/list`,
		data
	})
}

//根据教材ID查询教材详情
export function departmentRepositoryInfo(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/departmentRepository/info`,
		params
	})
}


// 老师签到列表
export function signInList(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/coursesign/list`,
		params
	})
}

// 老师 签到统计
export function courseSignStatistics(data) {
	return service.request({
	  method: 'post',
	  url: '/digital/controller/coursesignrecord/courseSignStatistics',
	  data
	});
}

//删除签到活动
export function deleteSignIn(data) {
	return service.request({
		method: 'post',
		url:`/digital/controller/coursesign/delete`,
		type:'2',
		data
	})
}


// 老师发起签到
export function createSignIn(data) {
	return service.request({
	  method: 'post',
	  url: '/digital/controller/coursesign/save',
	  data
	});
}
//老师 签到详情
export function querySignDetail(params) {
	return service.request({
	  method: 'get',
	  url: '/digital/controller/coursesign/querySignDetail',
	  params
	});
}

// 取消请假
export function courseSignRecordDelete(data) {
    return service.request({
        method: 'post',
        url: `/digital/controller/coursesignrecord/delete`,
        type: '2',
        data
    })
}

// 学生签到
export function stuSignIn(data) {
	return service.request({
	  method: 'post',
	  url: '/digital/controller/coursesignrecord/save',
	  data
	});
}
// 学生签到列表
export function signStudentList(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/coursesignrecord/list`,
		params
	})
}
// 学生签到列表详情
export function getSignInDetails(id) {
	return service.request({
	  method: 'get',
	  url: `/digital/controller/coursesignrecord/info/${id}`,
	})
}
//  学生 签到统计
export function stuCourseSignStatistics(data) {
	return service.request({
	  method: 'post',
	  url: '/digital/controller/coursesignrecord/queryCourseSignStatistics',
	  data
	});
}

//老师、学生答疑列表
export function AnswerList(params) {
	return service.request({
		method: 'get',
		url:`/digital/controller/digitalcoursetopic/listAnswer`,
		params
	})
}

// 提问
export function questionSave(data) {
	return service.request({
	  method: 'post',
	  url: '/aigc/controller/coursetopic/save',
	  data,
	})
}
//学生答疑详情
export function getAnswerInfo(id) {
	return service.request({
	  method: 'get',
	  url: `/digital/controller/digitalcoursetopic/infoAnswer/${id}`,
	})
}

//留言板列表
export function getMessageBoardList(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/coursetopic/listLeaveMessage',
	  params
	});
}
//新增留言
export function addMessageBoard(data) {
	return service.request({
	  method: 'post',
	  url: '/aigc/controller/coursetopic/save',
	  data
	});
}
//留言详情
export function getMessageDetails(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/coursetopic/infoLeaveMessage',
	  params
	})
  }
//留言板删除
export function deleteMessageBoard(data) {
	return service.request({
		method: 'post',
		url: `/aigc/controller/coursetopic/delete`,
		type: '2',
		data
	})
}
// 更新话题
export function updateTopic(data) {
	return service.request({
	  method: 'post',
	  url: '/aigc/controller/coursetopic/update',
	  data
	})
}
//我回复的，回复我的
export function getMessageBoardReplyList(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/coursetopic/listRecoverLeaveMessage',
	  params
	});
}
//点赞
export function updateLike(data) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/caseliked/updateLike',
	  params: data
	});
}
//教研室资源管理列表
export function resourceList(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/aigcresourcedetail/list',
	  params
	});
}

//教研室轮播列表
export function resourceBannerList(params) {
	return service.request({
	  method: 'get',
	  url: '/aigc/controller/advertisingspace/list',
	  params
	});
}