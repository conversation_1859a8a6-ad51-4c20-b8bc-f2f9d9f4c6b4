import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Back from '@/components/back.vue'
import 'element-plus/dist/index.css'
import ElementPlus from 'element-plus'
import './assets/styles/global.scss'
import './styles/public.scss'
import './static/base.css'
import pinia from '@/stores'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
// 引入阿里巴巴iconfont
import './assets/iconfont/iconfont.css'
import MsgDialog from './components/msgDialog.vue'
const app = createApp(App)
app.use(ElementPlus, {
  locale: zhCn,
})
// 全局组件挂载
app.component('Back', Back)
app.component('msg-dialog', MsgDialog)
app.use(router)
app.use(pinia)
app.mount('#app')