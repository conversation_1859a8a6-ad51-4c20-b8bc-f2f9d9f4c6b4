<template>
    <el-dialog 
    width="493px" 
    class="adddialog" 
    :title="dialogTitle" 
    v-model="dialogVisible" 
    @close="close" 
    :close-on-click-modal="false"
    :style="{
        '--el-dialog-padding-primary':'0px'
    }">
        <el-form :rules="rules" ref="addform" :model="form" label-width="100px">
                <el-form-item label="教材名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入教材名称" show-word-limit maxlength="50" clearable></el-input>
                </el-form-item>
                
                <el-form-item label="教材封面" prop="coverUrl">
                    <el-upload
                        class="upload-demo"
                        :action="getUrl"
                        name="file"
                        :limit="1"
                        accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
                        :on-remove="handleRemove"
                        :file-list="fileList"
                        :headers="headerUrl"
                        :on-success="handleAvatarSuccess"
                        :data="imgDetails"
                        list-type="picture"
                    >
                        <el-button class="defbtn">点击上传封面</el-button>
                        <template #tip >
                            <div class="el-upload__tip">
                                只能上传jpg/png文件，且不超过2M
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
                
                <el-form-item label="出版社名称" prop="pressName">
                    <el-input v-model="form.pressName" placeholder="请输入出版社名称" show-word-limit maxlength="50" clearable></el-input>
                </el-form-item>
                
                <el-form-item label="主编" prop="chiefEditor">
                    <el-input v-model="form.chiefEditor" placeholder="请输入主编姓名" show-word-limit maxlength="20" clearable></el-input>
                </el-form-item>
                
                <el-form-item label="ISBN" prop="isbn">
                    <el-input v-model="form.isbn" placeholder="请输入ISBN" show-word-limit maxlength="20" clearable></el-input>
                </el-form-item>
                
                <el-form-item label="价格" prop="price">
                    <el-input v-model="form.price" placeholder="请输入价格" clearable>
                        <template #append>元</template>
                    </el-input>
                </el-form-item>
                
                <el-form-item label="详情链接" prop="detailLink">
                    <el-input v-model="form.detailLink" placeholder="请输入详情链接" clearable></el-input>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button class="linebtn" @click="close">取 消</el-button>
                <el-button class="defbtn40" @click="onSubmit">确定</el-button>
            </div>
        </template>
        
    </el-dialog>
</template>

<script setup>
import { 
    getMyBookList, 
    saveMaterial, 
    getMaterialDetail, 
    updateMaterial, 
    deleteMaterial } from '@/api/expert/index.js'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const dialogTitle = ref('新增教材')
const levelMap = ref({
    1: '本科',
    2: '高职'
})
let form = reactive({
    id: '',
    name: '',
    coverUrl: '',
    coverName: '',
    coverSize: '',
    pressName: '',
    chiefEditor: '',
    isbn: '',
    price: '',
    detailLink: '',
})
const rules = ref({
    name: [
        { required: true, message: '请输入教材名称', trigger: 'blur' }
    ],
    coverUrl: [
        { required: true, message: '请上传教材封面', trigger: 'blur' }
    ],
    pressName: [
        { required: true, message: '请输入出版社名称', trigger: 'blur' }
    ],
    chiefEditor: [
        { required: true, message: '请输入主编姓名', trigger: 'blur' }
    ],
    isbn: [
        { required: true, message: '请输入ISBN', trigger: 'blur' }
    ],
    price: [
        { required: true, message: '请输入价格', trigger: 'blur' },
        { 
            pattern: /^[0-9]+(\.[0-9]{1,2})?$/, 
            message: '请输入正确的价格格式(最多两位小数)', 
            trigger: 'blur'
        }
    ],
    detailLink: [
        { 
            pattern: /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{1,64})?\.)+[a-z]{2,6}\/?/, 
            message: '请输入正确的链接格式', 
            trigger: 'blur' 
        }
    ]
})
const emits = defineEmits(['reload'])
const specialData = ref()
const addform = ref()
const show = () =>{
    dialogVisible.value = true;
}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateMaterial(form) : 
                saveMaterial(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(this.form.id ? '更新成功' : '保存成功');
                    dialogVisible.value = false;
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    fileList.value = fileList;
    form.coverUrl = '';
    form.coverName = '';
    form.coverSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    fileList.value = fileList;
    if (res.status == 0) {
        form.coverUrl = res.data.url;
        form.coverName = res.data.fileName;
        form.coverSize = res.data.size;
    } else {
        ElMessage.error('上传失败：' + res.msg);
    }
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>