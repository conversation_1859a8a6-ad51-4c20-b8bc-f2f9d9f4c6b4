<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <el-card class="card">
                <div class="top">
                    <div class="title">资源管理</div>
                    <el-button type="primary" @click="handleAdd" :icon="UploadFilled" class="upload">上传资源</el-button>
                </div>
                <div class="search-form">
                    <el-form :model="pageBean" inline label-width="10px">
                        <el-form-item label="">
                            <el-input
                                v-model="pageBean.name"
                                placeholder="请输入资源名称"
                                clearable
                                class="w200"
                            />
                        </el-form-item>

                        <el-form-item label="">
                            <el-select
                                v-model="pageBean.resourceType"
                                placeholder="请选择资源类型"
                                clearable
                                class="w200"
                            >
                                <el-option
                                    v-for="item in ResourceTypes"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="">
                            <el-select
                                v-model="pageBean.label"
                                placeholder="请选择资源标签"
                                clearable
                                class="w200"
                            >
                                <el-option
                                    v-for="item in resourceTags"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" @click="handleSearch" :icon="Search" class="search">搜索</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                

            <div class="table-container">
                    <el-table :data="tableData" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderColor" class="custom-table">
                        <el-table-column prop="name" label="资源名称" align="center" />

                        <el-table-column label="封面" align="center">
                            <template #default="scope">
                                <el-image
                                    v-if="scope.row.coverUrl"
                                    :src="scope.row.coverUrl"
                                    :preview-src-list="[scope.row.coverUrl]"
                                    style="width: 60px; height: 40px"
                                    fit="cover"
                                    preview-teleported
                                />
                                <span v-else class="no-cover">暂无封面</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="resourceType" label="类型" align="center">
                            <template #default="scope">
                                {{ ResourceTypesMap[scope.row.resourceType] }}
                            </template>
                        </el-table-column>

                        <el-table-column prop="label" label="标签" align="center">
                            <template #default="scope">
                                {{ resourceTagsMap[scope.row.label] || '---' }}
                            </template>
                        </el-table-column>

                        <el-table-column prop="uploader" label="上传人" align="center" />

                        <el-table-column prop="createTime" label="上传时间" align="center"  />

                        <el-table-column label="操作" width="150" fixed="right" align="center">
                            <template #default="scope">
                                <el-button
                                    type="primary"
                                    link
                                    @click="handleEdit(scope.row)"
                                >
                                    编辑
                                </el-button>
                                <el-divider direction="vertical" />
                                <el-button
                                    type="danger"
                                    link
                                    @click="handleDelete(scope.row)"
                                >
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="pageBean.pageNum"
                            v-model:page-size="pageBean.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </el-card>

        </div>

        <msg-dialog ref="deleteDialogRef" />
    </div>
</template>

<script setup>
import { Search,UploadFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ResourceTypes, ResourceTypesMap, resourceTags, resourceTagsMap } from '@/utils/static-tools'
import { resourceList } from '@/api/study'
const router = useRouter()

const pageBean = ref({
  // name: '',
  // resourceType: '',
  // label: '',
  pageNum: 1,
  pageSize: 10
})
const total = ref(0)

const loading = ref(false)
const deleteDialogRef = ref(null)

const tableData = ref([])

const tableHeaderColor = {
    background: '#F5F7FA !important',
    color: '#44474D',
    fontSize: '14px',
} 

const handleSearch = () => {
  pageBean.value.pageNum = 1
  loadData()
}


const handleAdd = () => {
  router.push('/jiaoyanshi/resources/add')
}

const handleEdit = (row) => {
  router.push({
    path: '/jiaoyanshi/resources/add',
    query: { id: row.id }
  })
}

const handleDelete = (row) => {
  deleteDialogRef.value.show({
    type: 'edit',
    title: '删除确认',
    msg: '确定要删除该资源吗？',
    leftBtnText: '取消',
    submitBtnText: '确定',
    submitAction: () => {
      console.log('删除资源:', row)
      // 显示删除成功提示
      deleteDialogRef.value.show({
        type: 'success',
        title: '删除成功',
        msg: '该资源已删除'
      })
    }
  })
}
const handleSizeChange = (val) => {
  pageBean.value.pageSize = val
  pageBean.value.pageNum = 1
  loadData()
}

const handleCurrentChange = (val) => {
  pageBean.value.pageNum = val
  loadData()
}

const loadData = () => {
  loading.value = true
  resourceList(pageBean.value).then(res => {
      tableData.value = res.data  
      total.value = res.page.total
      loading.value = false
  })
}

onMounted(() => {
  loadData()
});
</script>

<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}

.search-form {
  .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.table-container {
    margin-top: 20px;
  .no-cover {
    color: #999;
    font-size: 12px;
  }
  .pagination-container {
    margin-top: 20px;   
    display: flex;
    justify-content: center;
  }
}

.el-table {
  .el-button {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
  }
}
.w200 {
    width: 200px;
}
.card{
    margin-top: 20px;
}
.top{
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
}
.search{
    width: 80px;
    height: 36px;
    background: #386CFC;
    border-radius: 4px 4px 4px 4px;
}
.upload{
    width: 100px;
    height: 36px;
    background: #386CFC;
    border-radius: 4px 4px 4px 4px;
}

.custom-table {
    border: 1px solid #EBEEF5;
    border-radius: 8px;
    :deep(){
        .el-table__header{
            th {
                height: 56px;
            }
        }
        .el-table__body{
            td {
                height: 72px;
            }
            tr:not(:last-child) td {
            border-bottom: 1px solid #EBEEF5 !important;
            }
        }
    }
}
</style>