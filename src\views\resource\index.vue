<template>
  <div class="mdivcss">
    <div class="wid bgcss">
      <back>返回</back>
      <div class="bgmain">
        <div class="contentdiv">
          <h4 class="zititle">《国家安全教育大学生读本》出版发行</h4>
          <div class="flex">
            <div class="tag">教学资源</div>
            <span class="timetext">2024-06-20</span>
          </div>
          <div class="filebox" v-loading="isLoading">
            <iframe
              @load="load"
              class="iframebox"
              src="https://file.yunshangbook.com/onlinePreview?url=aHR0cHM6Ly93dXRvbmdodWEtb3NzLm9zcy1jbi1xaW5nZGFvLmFsaXl1bmNzLmNvbS9kaWdpdGFsLXBsYXRmb3JtL251bGwvMTcwMDkzMjA0MTM1NDc1NDY4Ny5kb2N4"
              frameborder="0"
            ></iframe>
            <!-- <div id="player">
              <video-player
                @ready="ready"
                class="video-player vjs-custom-skin"
                ref="videoPlayerRef"
                v-bind="playerOptions"
                :playsinline="true"
              >
              </video-player>
            </div> -->
          </div>
          <div class="cssfont">
            为推动总体国家安全观进教材进课堂进头脑，由教育部和中央有关部门组织编写的马克思主义理论研究和建设工程重点教材《国家安全教育大学生读本》（以下简称《读本》），已由高等教育出版社出版。2014年4月15日，习近平总书记在中央国家安全委员会第一次会议上，创造性提出总体国家安全观。10年来，以习近平同志为核心的党中央，把马克思主义国家安全理论同当代中国国家安全实践相结合、同中华优秀传统战略文化相结合，在新时代国家安全实践中不断深化理论创新。《读本》是第一部全面系统阐释总体国家安全观的统编教材，是高等学校开设国家安全教育公共基础课的权威用书。全书由导论和10章构成。导论主要介绍我国国家安全形势和大学生学习总体国家安全观的基本要求，第一至十章围绕总体国家安全观的理论体系、筑牢各重点领域安全屏障、新时代大学生践行总体国家安全观的实践要求等展开。《读本》充分反映总体国家安全观的重大意义、科学内涵、核心要义，充分反映新时代党领导国家安全工作的开创性成就和历史性变革，对于引导新时代大学生系统把握总体国家安全观、增强维护国家安全的意识和能力具有重要意义
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import back from '@/components/back.vue'
import videoPlayer from 'vue3-video-play'
import 'vue3-video-play/dist/style.css'
const playerOptions = reactive({
  width: '100%',
  height: '100%',
  autoplay: true,
  muted: false,
  loop: false,
  preload: 'auto',
  language: 'zh-CN',
  aspectRatio: '16:9',
  fluid: true,
  src: 'https://wutonghua-oss.oss-cn-qingdao.aliyuncs.com/goldcourse/manager/2024092307/第3课 自我认知.mp4',
  notSupportedMessage: '此视频暂无法播放，请稍后再试',
  controlBar: {
    timeDivider: true,
    durationDisplay: true,
    remainingTimeDisplay: false,
    fullscreenToggle: true,
  },
})
const isLoading = ref(true)
function load() {
  isLoading.value = false
}

function ready() {
  isLoading.value = false
}
</script>

<style lang="scss" scoped>
.iframebox {
  width: 100%;
  height: 822px;
}
.filebox {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 12px 12px 12px 12px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}
.tag {
  width: 76px;
  height: 27px;
  background: #f5f7fa;
  border-radius: 4px 4px 4px 4px;
  text-align: center;
  line-height: 27px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #386cfc;
  margin-right: 30px;
}
.wid {
  width: 1280px;
}
.bgcss {
  margin: 0px auto;
  margin-bottom: 40px;
}
.mdivcss {
  padding: 20px 0px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 93px);
}
.bgmain {
  background: #ffffff;
  margin-top: 20px;
}
.contentdiv {
  margin: 0 auto;
  min-height: 441px;
  background: #fff;
  padding: 30px;
}
.zititle {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  line-height: 34px;
  text-align: center;
  margin-bottom: 17px;
}
.timetext {
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  color: #666;
}
.line {
  width: 100%;
  height: 1px;
  background: #e5e5e5;
  margin: 20px 0;
}
.cssfont {
  margin-top: 30px;
  font-size: 16px;
  color: #4b4d4b;
  line-height: 32px;
  text-align: justify;
  display: -webkit-box;
  overflow-wrap: break-word;
  overflow: hidden;
  -webkit-box-orient: vertical;
}
</style>