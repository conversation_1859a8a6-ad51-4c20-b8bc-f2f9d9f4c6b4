import service from '@/utils/request.js'
export function exlertLiveList(data) {
  return service.request({
    method: 'get',
    url: '/ear/portal/courseinfo/list',
    params: data
  });
}

export function offLectureList(data) {
  return service.request({
    method: 'get',
    url: '/aigc/business/activity/list',
    params: data
  });
}
// 教研室列表
export function jiaoyanlist(data) {
  return service.request({
    method: 'get',
    url: '/aigc/controller/teachingresearchoffice/list',
    params: data
  });
}
//新增教研室
export function saveTeachingOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/teachingresearchoffice/save',
    data,
		headers: {
			'Content-Type': 'application/json;charset=UTF-8'
		},
  });
}

// 更新教研室
export function updateTeachingOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/controller/teachingresearchoffice/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 教研室详情
export function jyinfo(id){
  return service.request({
    method: 'get',
    url: `/aigc/controller/teachingresearchoffice/info/${id}`,
  });
}
export function offLectureInfo(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/expertchairinfo/info?id=${data.id}&userId=${data.userId}`,
  });
}
export function xianxiaInfo(data) {
  return service.request({
    method: 'get',
    url: `/ear/portal/expertchairinfo/info?id=${data.id}`,
  });
}
export function jiaoyanInfo(data) {
  return service.request({
    method: 'get',
    url: `/aigc/business/activity/info?id=${data.id}`,
  });
}
// 教研室详情
export function jiaoyanDetail(data) {
  return service.request({
    method: 'get',
    url: `/aigc/business/teachingresearchoffice/info/${data.id}`,
  });
}
// 教研室详情统计信息
export function jiaoyanDetailInfo(data) {
  return service.request({
    method: 'get',
    url: `/aigc/controller/countInfo/getCountInfo/${data.id}`,
  });
}

export function offLectureSave(data) {
  return service.request({
    method: 'post',
    url: '/ear/portal/expertchairuserrelation/save',
    data
  });
}



//标签类型管理
export function labelList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/business/tagitem/listAll',
    params
  })
}




// 获取教研室主理人列表
export function getResearchOfficeUsers(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/teachingresearchoffice/getAllResarchOfficeUser',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 获取教研室资源列表
export function getResourceList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/business/resourcedetail/list',
    params,
  });
}
// 保存资源
export function saveResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 申请加入教研室
export function applyJoinResearchOffice(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/teachingresearchoffice/applyResarchOfficeUser',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}


// 更新教研室用户状态
export function updateOfficeUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/teachingresearchoffice/updateOfficeUser',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 主理人详情
export function getUserInfo(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/info`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
export function getPersonInf(data) {
  return service.request({
    method: 'post',
    url: `/aigc/business/userlogin/info`,
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 关注用户
export function followUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 取消关注用户
export function unfollowUser(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}
// 获取关注和粉丝数量
export function getFansCount(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/fans/getCount',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取资源详情
export function getResourceDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/resourcedetail/info/${id}`,
  });
}

// 更新资源
export function updateResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除资源
export function deleteResource(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/resourcedetail/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取我的教材列表
export function getMyBookList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/business/materialinfo/list',
    params,
  });
}

// 保存教材
export function saveMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取教材详情
export function getMaterialDetail(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/materialinfo/info/${id}`,
  });
}

// 更新教材
export function updateMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除教材
export function deleteMaterial(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/materialinfo/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取活动列表
export function getActivityList(params) {
  return service.request({
    method: 'get',
    url: '/aigc/business/activity/list',
    params
  });
}

// 保存活动
export function saveActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/activity/save',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 更新活动
export function updateActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/activity/update',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 删除活动
export function deleteActivity(data) {
  return service.request({
    method: 'post',
    url: '/aigc/business/activity/delete',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
  });
}

// 获取活动详情
export function getActivityInfo(id) {
  return service.request({
    method: 'get',
    url: `/aigc/business/activity/info?id=${id}`,
  });
}