<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">培训进修</div>
      <el-button v-if="isShow" class="addbtns" size="small" @click="showAdd"
        >添加培训进修</el-button
      >
    </div>
    <div class="listdiv">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="trainProject" label="项目名称">
        </el-table-column>
        <el-table-column prop="trainTopic" label="培训主题"> </el-table-column>
        <el-table-column prop="sponsor" label="主办方"> </el-table-column>
        <el-table-column prop="organizer" label="承办方"> </el-table-column>
        <el-table-column prop="startTime" label="开始时间"> </el-table-column>
        <el-table-column prop="endTime" label="结束时间"> </el-table-column>
        <el-table-column
          prop="fileUrl"
          label="结业证书"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.fileUrl"
              :src="scope.row.fileUrl"
              :preview-src-list="[scope.row.fileUrl]"
            >
            </el-image>
          </template>
        </el-table-column>
        <el-table-column
          prop="edit"
          label="操作"
          width="180"
          align="center"
          v-if="isShow"
        >
          <template slot-scope="scope">
            <el-button size="small" type="text" @click="editAction(scope.row)"
              >编辑</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button
              size="small"
              type="text"
              class="delbtn"
              @click="deleteAction(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="adddialog"
      width="493px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="项目名称：" prop="trainProject">
          <el-input
            placeholder="请输入项目名称"
            v-model="form.trainProject"
          ></el-input>
        </el-form-item>
        <el-form-item label="培训主题：" prop="trainTopic">
          <el-input
            placeholder="请输入培训主题"
            v-model="form.trainTopic"
          ></el-input>
        </el-form-item>
        <el-form-item label="主办方：" prop="sponsor">
          <el-input
            placeholder="请输入主办方"
            v-model="form.sponsor"
          ></el-input>
        </el-form-item>
        <el-form-item label="承办方：" prop="organizer">
          <el-input
            placeholder="请输入承办方"
            v-model="form.organizer"
          ></el-input>
        </el-form-item>
        <el-form-item label="参与时间：" prop="joinTime">
          <el-date-picker
            v-model="form.joinTime"
            class="wid100"
            type="daterange"
            placeholder="选择日期"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :value-format="'yyyy-MM-dd'"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结业证书：" prop="fileUrl">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            :headers="headers"
            :data="fileData"
            :accept="'.jpeg,.jpg,png,.JPEG,.JPG,.PNG'"
            :on-success="handleSuccess"
            multiple
            :limit="1"
            :file-list="fileList"
          >
            <el-button class="defbtn" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="btns">
          <el-button class="linebtn" @click="close2">取 消</el-button>
          <el-button class="defbtn40" @click="submitAction">保 存</el-button>
        </div>
        
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  trainingList,
  trainingfurtherSave,
  trainingfurtherDelete,
  trainingfurtherInfo,
  trainingfurtherEdit,
} from '@/api/center/training'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const isShow = ref(false)
const dialogFormVisible = ref(false)
const isEdit = ref(false)
const editId = ref(null)
const dTitle = ref('')
var fileList = ref([])
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const headers = ref({
  Authorization: localStorage.getItem('token') 
})
const fileData = ref({ serviceName: 'web' })
const pageBean = reactive({
  pageNum: 1,
  pageSize: 4,
})
const tableData = ref([])
const total = ref(0)
let form = reactive({
    id:'',
    trainProject: '',
    trainTopic: '',
    joinTime: [],
    fileUrl: '',
    fileName: '',
    fileSize: '',
    startTime: '',
    endTime: '',
    sponsor: '',
    organizer: '',
})
const rules = ref({
  trainProject: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  sponsor: [{ required: true, message: '请输入主办方', trigger: 'blur' }],
  organizer: [
    { required: true, message: '请输入承办方', trigger: 'blur' },
  ],

  trainTopic: [
    { required: true, message: '请输入培训主题', trigger: 'blur' },
  ],
  joinTime: [
    { required: true, message: '请选择参与时间', trigger: 'change' },
  ],
  fileUrl: [
    { required: true, message: '请上传结业证书', trigger: 'blur' },
  ],
})


function loaddata() {
  trainingList(pageBean).then((result) => {
    tableData.value = result.data
    total.value = result.page.total
  })
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  editId.value = ''
  dialogFormVisible.value = true
  dTitle.value = '添加培训进修'
  let obj = form
  Object.keys(obj).forEach((key) => {
    obj[key] = ''
  })
}
function editAction(row) {
  dialogFormVisible.value = true
  dTitle.value = '编辑培训进修'
  isEdit.value = true
  editId.value = row.id
  getDetailInfo(row.id)
}
function getDetailInfo(id) {
  trainingfurtherInfo(id).then((result) => {
    if (result.data) {
      Object.keys(form).forEach(key => {
        if (result.data[key]) {
          form[key] = result.data[key]
        }
      });
      fileList.value = [{ url: form.fileUrl, name: form.fileName }]
      form.joinTime.push(form.startTime)
      form.joinTime.push(form.endTime)
    } else {
      ElMessage.error('获取详情失败')
    }
  })
}
const myform = ref()
function close2() {
  dialogFormVisible.value = false
  fileList.value = []
  nextTick(() => {
    myform.value.resetFields()
  })
}
function deleteAction(row) {
      ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          trainingfurtherDelete({ id: row.id }).then((result) => {
            if (result.data) {
              ElMessage({
                type: 'success',
                message: '删除成功!',
              })
              loaddata()
            } else {
              ElMessage.error(result.msg)
            }
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消删除',
          })
        })
    }
function handleSuccess(res, file) {
      if (res.data.url.length > 0) {
        form.fileName = res.data.fileName
        form.fileSize = res.data.size
        form.fileUrl = res.data.url
      } else {
        ElMessage.error(res.msg||'上传失败')
      }
    }
function submitAction() {
  myform.value.validate((valid) => {
    if (valid) {
      form.startTime = form.joinTime[0]
      form.endTime = form.joinTime[1]
      if (form.id) {
        delete form.createTime
      }
      const action = form.id
        ? trainingfurtherEdit
        : trainingfurtherSave
      action(form).then((result) => {
        if (result.data) {
          ElMessage.success(this.form.id ? '编辑成功' : '保存成功')
          loaddata()
          close2()
        } else {
          ElMessage.error(result.msg)
        }
      })
    } else {
      return false
    }
  })
}
onMounted(()=>{
    pageBean.createBy = route.query.id
    if (localStorage.getItem('id') == route.query.id) {
      isShow.value = true
    }
    loaddata()
})
</script>

<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
}
.delbtn {
  color: #f56c6c;
  cursor: pointer;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #386CFC;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right:10px;
  background-color: #386CFC;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:hover {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:focus {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.mydialog{
  ::v-deep(.el-dialog__body) {
    overflow: auto;
    
  }
  .el-input{
    ::v-deep(.el-input__inner){
      padding:0px 10px !important;
    }
    
  }
  
}
::v-deep(.el-date-editor){
  padding-left: 10px !important;
}
</style>