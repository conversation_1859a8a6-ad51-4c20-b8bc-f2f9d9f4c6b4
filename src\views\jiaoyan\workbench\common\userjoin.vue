<template>
    <el-dialog 
    width="1080px" 
    class="adddialog"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
    title="报名信息" 
    v-model="dialogVisible" 
    @close="close" 
    :close-on-click-modal="false">
        <div>
            <el-table class="mytable" :data="tableData" style="margin-top: 20px;">
                <el-table-column prop="name" label="姓名" align="center" min-width="80px" />
                <el-table-column prop="name" label="年龄" align="center" width="80px" />
                <el-table-column prop="name" label="性别" align="center" width="80px"/>
                <el-table-column prop="name" label="属性" align="center" width="80px"/>
                <el-table-column prop="name" label="高校/企业" align="center" width="183px"  show-overflow-tooltip />
                <el-table-column prop="name" label="部门" align="center" min-width="183px" />
                <el-table-column prop="name" label="职称" align="center" width="80px"/>
                <el-table-column prop="name" label="权限" align="center" width="80px"/>
                <el-table-column prop="createTime" label="报名时间" align="center" width="157px"  />
            </el-table>
            <el-pagination
            v-model:current-page="pageBean.pageNum"
            v-model:page-size="pageBean.pageSize"
            :background="true"
            layout="total, prev, pager, next, jumper"
            :total="total"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; justify-content: center;"/>
        </div>
        
        
    </el-dialog>
</template>

<script setup>
import { 
    getMyBookList, 
    saveMaterial, 
    getMaterialDetail, 
    updateMaterial, 
    deleteMaterial } from '@/api/expert/index.js'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const dialogVisible = ref(false)
const tableData = ref([
    {name:'赵湾湾'}
])
let pageBean = reactive({
    pageNum:1,
    pageSize:10
})
const total = ref(0)
const show = () =>{
    dialogVisible.value = true;
}
function close() {
    dialogVisible.value = false
}
function loadData(){

}
function handleCurrentChange(page){
    pageBean.pageNum = page
    loadData()
}

onMounted(()=>{
    loadData()
})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>

<style lang="scss">

</style>