<template>
  <div class="headerBg">
    <div class="mainHeader">
      <div>
        <span @click="toHome">
          <img class="firstlogo" src="../assets/logo.png" />
        </span>
      </div>
      <div class="menudiv">
        <el-menu
          :default-active="activeIndex"
          :router="true"
          active-text-color="#386CFC"
          mode="horizontal"
          class="el-menu-title"
        >
          <el-menu-item index="/home">首页</el-menu-item>
          <el-menu-item index="/jiaoyanshi">教研室</el-menu-item>
          <el-menu-item index="/coursecenter">课程资源</el-menu-item>
          <el-menu-item index="/teachproject">培训项目</el-menu-item>
          <el-menu-item index="/teachtool">AI工具</el-menu-item>
          <el-menu-item index="/jxdandai">教师档案袋</el-menu-item>
        </el-menu>
      </div>
      <div class="user">
        <div class="loginRegister" v-show="loginBut">
          <div class="mt">
            <div class="logincss" @click="logindialogVisible = true">
              <span class="loginButton">登录</span>
            </div>

            <div class="registercss" @click="registerdialogVisible = true">
              <span class="registerButton"> 注册 </span>
            </div>
          </div>
        </div>

        <div class="touxiang" v-show="useUserStore.userType">
          <div class="touxiangPic">
            <img
              v-if="useUserStore.getPicUrl"
              :src="useUserStore.getPicUrl"
              alt
            />
            <img v-else src="../assets/touxiang1.jpg" alt="" />
          </div>
          <div class="loginName">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link" v-if="useUserStore.loginAccount">{{
                useUserStore.loginAccount
              }}</span>
              <span v-else-if="useUserStore.nickname">{{
                useUserStore.nickname
              }}</span>
              <span v-else class="el-dropdown-link">教师职称</span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="spacePer"
                    >个人中心</el-dropdown-item
                  >
                  <el-dropdown-item command="loginOut"
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title
      v-model="logindialogVisible"
      width="493px"
      class="commonDialog"
      :show-close="false"
    >
      <template #header="{ close }">
        <div class="my-header">
          <div class="tabbox">
            <div
              @click="tabHandle(index)"
              class="tabItem"
              v-for="(item, index) in tabList"
              :class="{ activeItem: index == activeItemIndex }"
            >
              {{ item }}
            </div>
          </div>
          <img class="close" src="../assets/close.png" alt="" @click="close" />
        </div>
      </template>
      <div class="login">
        <el-form
          v-if="activeItemIndex == 0"
          :model="ruleForm"
          :rules="rules"
          ref="formRefAccount"
          label-width="64px"
          label-position="top"
        >
          <el-form-item label="账号:" prop="username">
            <el-input
              v-model.trim="ruleForm.username"
              clearable
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="密码:" prop="password">
            <el-input
              placeholder="请输入密码"
              v-model.trim="ruleForm.password"
              clearable
              type="password"
              show-password
            ></el-input>
          </el-form-item>
        </el-form>

        <el-form
          v-if="activeItemIndex == 1"
          :model="ruleForm"
          :rules="rules"
          ref="formRefCode"
          label-width="64px"
          label-position="top"
        >
          <el-form-item label="手机号:" prop="username">
            <el-input
              v-model="ruleForm.username"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="验证码:" prop="password">
            <el-input
              placeholder="请输入验证码"
              v-model="ruleForm.password"
              type="password"
            >
              <template #suffix> <sendCode></sendCode></template>
            </el-input>
          </el-form-item>
        </el-form>

        <el-button class="saveButton" type="primary" @click="submitForm()"
          >立即登录</el-button
        >
        <div class="loginTip">
          <span>没有账号？</span>
          <span @click="register()">立即注册</span>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="registerdialogVisible"
      width="493px"
      :before-close="handleCloseRegister"
      class="registerDialog"
      :show-close="false"
    >
      <template #header="{ close }">
        <div class="my-header">
          <p class="dtitle">注册</p>
          <img class="close" src="../assets/close.png" alt="" @click="close" />
        </div>
      </template>
      <div class="">
        <el-form
          :model="ruleFormRegister"
          :rules="rulesRegister"
          ref="ruleForm"
          label-width="64px"
          label-position="top"
        >
          <el-form-item label="手机号:" prop="mobilephone">
            <el-input
              v-model="ruleForm.mobilephone"
              placeholder="请输入手机号"
              class="mobilephone"
            ></el-input>
          </el-form-item>
          <el-form-item label="验证码:" prop="messageAuthCode">
            <el-input
              v-model="ruleForm.messageAuthCode"
              placeholder="请输入验证码"
            >
              <template #suffix> <sendCode></sendCode></template
            ></el-input>
          </el-form-item>
        </el-form>
        <el-button class="saveButton" type="primary" @click="registerHandle"
          >立即注册</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      v-model="infodialogVisible"
      width="493px"
      class="infoDialog"
      :show-close="false"
    >
      <template #header>
        <div class="my-header">
          <p class="dtitle">信息补充</p>
          <!-- <img class="close" src="../assets/close.png" alt="" @click="close" /> -->
        </div>
      </template>
      <div class="infobox">
        <el-form
          :model="ruleInfoForm"
          :rules="rulesInfo"
          ref="ruleForm"
          label-width="64px"
          label-position="right"
        >
          <el-form-item label="姓名:" prop="mobilephone">
            <el-input
              maxlength="4"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的姓名"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="年龄:" prop="mobilephone">
            <el-input
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的年龄"
              class="mobilephone"
            ></el-input>
          </el-form-item>
          <el-form-item label="性别:">
            <el-select
              class="sexselect"
              v-model="ruleForm.region"
              placeholder="请选择您的性别"
            >
              <el-option label="1" value="男" />
              <el-option label="2" value="女" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-radio-group v-model="ruleInfoForm.type">
              <el-radio value="1">高校</el-radio>
              <el-radio value="2">企业</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="单位:">
            <!-- <el-select
              class="sexselect"
              v-model="ruleForm.region"
              placeholder="请选择您的单位"
            >
              <el-option label="Zone one" value="shanghai" />
              <el-option label="Zone two" value="beijing" />
            </el-select> -->
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的姓名"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="部门:" prop="mobilephone">
            <el-input
              maxlength="20"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的部门"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="职称:" prop="mobilephone">
            <el-input
              maxlength="10"
              v-model="ruleForm.mobilephone"
              placeholder="请输入您的职称"
              class="mobilephone"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <el-button class="saveButton" type="primary">立即保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { logout } from '@/api/api.js'
import { userStore } from '@/stores/user.js'
import sendCode from '@/components/sendCode.vue'
import { toMd5 } from '@/utils/md5'
import {
  login,
  verifyMobilePhone,
  userRegister,
  messageAuthCodeLogin,
  sendMessage,
  registerLogin,
} from '@/api/index.js'
const route = useRoute()
const router = useRouter()
const useUserStore = userStore()
let loginBut = ref(true)
const activeIndex = computed(() => {
  console.log(route)
  return route.meta.activePath ? route.meta.activePath : route.path
})
let logindialogVisible = ref(false)
const handleClose = () => {
  logindialogVisible.value = false
}

const activeItemIndex = ref(0)
const tabList = ref(['账号登录', '手机号登录'])

function tabHandle(index) {
  activeItemIndex.value = index
}

function toHome() {
  router.replace('/home')
}
const registerdialogVisible = ref(false)
function registerHandle() {
  console.log('zhuce')
}
const handleCloseRegister = () => {
  registerdialogVisible.value = false
}
const rulesRegister = {
  mobilephone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  messageAuthCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}
const ruleFormRegister = reactive({
  mobilephone: '',
  messageAuthCode: '',
})

const ruleForm = reactive({
  username: '',
  password: '',
})
const validatePhone = (rule, value, callback) => {
  // 手机号正则表达式：以1开头，第二位为3-9，后面跟9位数字，共11位
  const phoneReg = /^1[3-9]\d{9}$/
  if (!value) {
    return callback(new Error('请输入手机号'))
  }
  if (!phoneReg.test(value)) {
    return callback(new Error('请输入正确的手机号格式'))
  }
  callback()
}
const rules = reactive({
  username: [
    {
      required: true,
      trigger: 'blur',
      validator: validatePhone,
    },
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

const formRefAccount = ref('')
const formRefCode = ref('')

function loginHandle() {
  formRefAccount.value.validate(async (valid, fields) => {
    if (valid) {
      let res = await login({
        username: ruleForm.username,
        password: toMd5(ruleForm.password),
        captcha: 1,
        rememberMe: false,
      })
      if (res.status == 0) {
        ElMessage.success('登录成功')
        userHandle(res)
        logindialogVisible.value = false
        loginBut.value = false
      } else {
        ElMessage.error(res.msg)
      }
    }
  })
}
const submitForm = () => {
  if (activeItemIndex.value == 0) {
    loginHandle()
  } else if (activeItemIndex.value == 1) {
  }
}

if (useUserStore.userType) {
  loginBut.value = false
} else {
  loginBut.value = true
}
const userHandle = (res) => {
  if (res.status == 0) {
    useUserStore.changeLoginAccount(res.data.loginAccount)
    useUserStore.changePicUrl(res.data.logo)
    useUserStore.changeNickName(res.data.name)
    useUserStore.changeUserType(res.data.userType)
    useUserStore.changeSchoolId(res.data.schoolId)
    localStorage.setItem('id', res.data.id)
    localStorage.setItem('token', res.data.token)
  } else {
    ElMessage.error(res.msg)
  }
}

function logoutClear() {
  localStorage.clear()
  loginBut.value = true
  ElMessage.success('已退出登录')
  useUserStore.clearState()
  router.push('/home')
}

// 登出功能
function handleLogout() {
  logout()
    .then((res) => {
      logoutClear()
    })
    .catch((error) => {
      logoutClear()
    })
}

function handleCommand(command) {
  if (command == 'spacePer') {
    router.push({ path: '/center' })
  } else if (command == 'loginOut') {
    handleLogout()
  }
}

function register() {
  registerdialogVisible.value = true
  logindialogVisible.value = false
}

const infodialogVisible = ref(false)
const ruleInfoForm = ref({
  type: '1',
})
const rulesInfo = {
  mobilephone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  messageAuthCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}
</script>

<style>
.commonDialog .el-dialog__body {
  padding: 0 40px !important;
}
.commonDialog,
.registerDialog {
  padding: 0 !important;
  height: 387px;
}
.registerDialog .el-dialog__body {
  padding: 0 40px !important;
}
.infoDialog {
  padding: 0;
  width: 493px;
  background: #ffffff;
  box-shadow: 0px 2px 20px 0px rgba(33, 58, 128, 0.16);
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
}
.infoDialog .el-dialog__body {
  padding: 30px;
  padding-top: 10px;
}
.infoDialog .el-dialog__header {
  padding-bottom: 0px;
}
.infoDialog label {
  height: 40px;
  line-height: 40px;
}
</style>
<style lang="scss" scoped>
.activeItem {
  position: relative;
  color: #386cfc !important;
}
.activeItem::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 8px;
  width: 50%;
  height: 3px;
  background-color: #386cfc;
  transform: translateX(-50%);
}
.tabItem {
  margin-right: 40px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #878d99;
  cursor: pointer;
}
.tabbox {
  display: flex;
  padding-left: 30px;
}
:deep(.el-input) {
  height: 40px;
}
.saveButton {
  width: 100%;
  height: 42px;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  line-height: 42px;
  margin-top: 22px;
}
.sexselect {
  height: 40px;
  :deep(.el-select__wrapper) {
    height: 40px;
  }
}
.close {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  right: 24px;
}
.dtitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
  padding-left: 30px;
}
.my-header {
  position: relative;
  padding-top: 20px;
  display: flex;
  width: 100%;
  height: 71px;
  background: linear-gradient(180deg, #e7edfe 0%, rgba(231, 237, 254, 0) 100%);
  justify-content: space-between;
}
:deep(.el-input__wrapper) {
  padding: 0 15px !important;
}
.flex {
  display: flex;
}
.firstlogo {
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
}
.el-dropdown {
  display: inline-block;
  position: relative;
  color: #606266;
  font-size: 14px;
  height: 42px;
  line-height: 42px;
  outline: none;
  span {
    outline: none;
  }
}
.mt {
  margin-top: 5px;
  display: flex;
}
.loginRegister {
  text-align: center;
}
.logincss {
  width: 60px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  margin-right: 16px;
  cursor: pointer;
}
.registercss {
  width: 60px;
  height: 33px;
  line-height: 33px;
  text-align: center;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #386cfc;
  cursor: pointer;
}
.loginButton {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}
.registerButton {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #386cfc;
}
.headerBg .mainHeader .user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  float: right;
  color: #333;
}
.mainHeader {
  padding: 0 80px;
  color: #111111;
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 100%;
}

.menudiv {
  display: inline-block;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-left: 0;
  width: 821px;
  margin-left: 66px;
}
.headerBg {
  width: 100%;
  height: 58px;
  filter: progid:DXImageTransform.Microsoft.Shadow(color=#909090,direction=120,strength=4);
  -moz-box-shadow: 2px 2px 10px #f1f1f1;
  -webkit-box-shadow: 2px 2px 10px #f1f1f1;
  box-shadow: 1px 1px 10px #f1f1f1;
  background: #ffffff;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.08);
  .main {
    color: #111111;
    font-size: 13px;
    .user {
      margin-top: 5px;
      margin-left: 550px;
      display: flex;
      width: 73px;
      font-size: 13px;
      color: rgba(51, 51, 51, 1);
      :nth-child(1) {
        margin-right: 8px;
      }
      :nth-child(2) {
        margin-right: 8px;
      }
    }
  }
}
.touxiang {
  float: right;
  overflow: hidden;
  height: 38px;
  margin-top: 4px;

  .touxiangPic {
    float: left;
    margin-right: 10px;
    height: 38px;
    width: 38px;
    overflow: hidden;
    border-radius: 50%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .loginName {
    float: left;
    height: 38px;
    line-height: 42px;
    color: #222222;
    font-size: 14px;
    overflow: hidden;
    cursor: pointer;
  }
}
.login {
  .loginTip {
    margin: 20px 0 0;
    font-size: 14px;
    color: rgba(51, 51, 51, 1);
    span:nth-child(2) {
      font-size: 14px;
      color: #386cfc;
      cursor: pointer;
    }
  }
}
</style>
