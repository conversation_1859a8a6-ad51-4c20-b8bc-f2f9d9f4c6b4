<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <el-card class="card">
                <div class="title">资源上传</div>
                <el-form
                    ref="formRef"
                    :model="formData"
                    :rules="rules"
                    label-width="120px"
                    class="resource-form"
                >
                    <el-form-item label="资源名称：" prop="resourceName">
                        <el-input
                            v-model="formData.resourceName"
                            placeholder="请输入资源名称"
                            clearable
                            class="w-full"
                        />
                    </el-form-item>
                   <el-form-item label="主讲人：" prop="speaker">
                        <el-input
                            v-model="formData.speaker"
                            placeholder="请输入主讲人"
                            clearable
                            class="w-full"
                        />
                    </el-form-item>

                    <el-form-item label="资源类型：" prop="resourceType">
                        <el-select
                            v-model="formData.resourceType"
                            placeholder="请选择资源类型"
                            clearable
                            class="w-full"
                        >
                            <el-option
                                v-for="item in ResourceTypes"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="资源标签：" prop="resourceTag">
                        <el-select
                            v-model="formData.resourceTag"
                            placeholder="请选择资源标签"
                            clearable
                            class="w-full"
                        >
                            <el-option
                                v-for="item in resourceTags.filter(tag => tag.id !== '')"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="资源文件：" prop="resourceFiles">
                        <el-upload
                            v-model:file-list="formData.resourceFiles"
                            :action="uploadUrl"
                            :on-success="handleResourceFileSuccess"
                            :on-error="handleUploadError"
                            :on-remove="handleResourceFileRemove"
                            :before-upload="beforeUpload"
                            multiple
                            :limit="1"
                            :on-exceed="handleExceed"
                        >
                            <el-button type="primary" class="chess" :icon="Plus">点击上传资源</el-button>
                            <template #tip>
                                <div class="upload-tip">
                                  支持doc、docx、ppt、pptx、pdf、mp4等格式文件，单个文件不超过500m
                                </div>
                            </template>
                        </el-upload>
                    </el-form-item>

                    <el-form-item label="资源封面：" prop="coverImage">
                        <el-upload
                            v-model:file-list="formData.coverImage"
                            :action="uploadUrl"
                            :on-success="handleCoverImageSuccess"
                            :on-error="handleUploadError"
                            :on-remove="handleCoverImageRemove"
                            :before-upload="beforeImageUpload"
                            list-type="picture-card"
                            :limit="1"
                            :on-exceed="handleExceed"
                        >
                            <el-icon><Plus /></el-icon>
                            <template #tip>
                                <div class="upload-tip">
                                  只能上传jpg、png文件，且不超过2M，最优比例16:9
                                </div>
                            </template>
                        </el-upload>
                    </el-form-item>

                    <el-form-item label="更多介绍：" >
                        <editor ref="editorRef" />
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleSubmit" :loading="submitLoading" class="confirm">
                            确认上传
                        </el-button>
                        <el-button @click="handleCancel" class="cancel">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { ResourceTypes, resourceTags } from '@/utils/static-tools'
import { Plus } from '@element-plus/icons-vue'
import editor from '@/components/editor.vue'

const router = useRouter()
const formRef = ref()
const editorRef = ref()
const submitLoading = ref(false)

// 上传地址
const uploadUrl = import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'

const formData = ref({
  resourceName: '',
  speaker: '',
  resourceType: '',
  resourceTag: '',
  resourceFiles: [],
  coverImage: [],
  description: ''
})

const rules = ref({
  resourceName: [
    { required: true, message: '请输入资源名称', trigger: 'blur' }
  ],
  speaker: [
    { required: true, message: '请输入主讲人', trigger: 'blur' }
  ],
  resourceType: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  resourceTag: [
    { required: true, message: '请选择资源标签', trigger: 'change' }
  ],
  resourceFiles: [
    { required: true, message: '请上传资源文件', trigger: 'change' }
  ],
  coverImage: [
    { required: true, message: '请上传资源封面', trigger: 'change' }
  ],

})

const handleSubmit = () => {
  // 获取富文本编辑器内容
  if (editorRef.value) {
    formData.value.description = editorRef.value.getContentHtml
  }

  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true

      setTimeout(() => {
        ElMessage.success('资源上传成功！')
        submitLoading.value = false
        router.push('/jiaoyanshi/resources')
      }, 2000)
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}


const handleCancel = () => {
  router.push('/jiaoyanshi/resources')
}

// 文件上传前的检查
const beforeUpload = (file) => {
  const isLt500M = file.size / 1024 / 1024 < 500
  if (!isLt500M) {
    ElMessage.error('上传文件大小不能超过 500MB!')
    return false
  }
  return true
}

// 图片上传前的检查
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 资源文件上传成功
const handleResourceFileSuccess = (response, _file, fileList) => {
  if (response.status == 0) {
    ElMessage.success('文件上传成功')
    formData.value.resourceFiles = fileList
  } else {
    ElMessage.error(response.message)
  }
}

// 封面图片上传成功
const handleCoverImageSuccess = (response, _file, fileList) => {
  if (response.status == 0) {
    ElMessage.success('封面上传成功')
    formData.value.coverImage = fileList
  } else {
    ElMessage.error(response.message)
  }
}

// 上传失败
const handleUploadError = () => {
  ElMessage.error('上传失败，请重试')
}

// 移除资源文件
const handleResourceFileRemove = (_file, fileList) => {
  formData.value.resourceFiles = fileList
}

// 移除封面图片
const handleCoverImageRemove = (_file, fileList) => {
  formData.value.coverImage = fileList
}

// 文件数量超出限制
const handleExceed = () => {
  ElMessage.warning('文件数量超出限制')
}
</script>

<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}

.card {
  margin-top: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.resource-form {
  max-width: 800px;

  .w-full {
    width: 365px;
    height: 42px;
  }


  .upload-tip {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #878D99;
    line-height: 1.4;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-textarea {
    .el-textarea__inner {
      resize: vertical;
    }
  }
}
:deep(){
  .el-form-item__label{
    padding-right: 0 !important;
  }
  .el-select__wrapper{
    height: 42px;
  }
  .el-upload-list__item{
    height: 31px;
    background: #F5F7FA;
    border-radius: 4px 4px 4px 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
  }
}

.chess{
  width: 136px;
  height: 42px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
  margin-bottom: 8px;
}
.cancel{
  width: 120px;
  height: 42px;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #386CFC;
  margin-left: 16px;
}
.confirm{
  width: 120px;
  height: 42px;
  background: #386CFC;
  border-radius: 4px 4px 4px 4px;
}
</style>