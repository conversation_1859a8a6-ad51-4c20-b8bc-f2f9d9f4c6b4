<template>
    <el-dialog 
    width="493px" 
    class="adddialog"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
    :title="dialogTitle" 
    v-model="dialogVisible" 
    @close="close" 
    :close-on-click-modal="false">
        <el-form :rules="rules" ref="addform" :model="form" label-width="100px">
                <el-form-item label="标题" prop="name">
                    <el-input v-model="form.name" placeholder="请输入标题" show-word-limit maxlength="50" clearable></el-input>
                </el-form-item>
                <el-form-item label="类型" prop="name">
                    <el-select v-model="form.type">
                        <el-option v-for="item in list" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="轮播图" prop="coverUrl">
                    <el-upload
                        class="upload-demo"
                        :action="getUrl"
                        name="file"
                        :limit="1"
                        accept=".jpg, .png, .JPG, .jpeg, .JPEG, .PNG, .gif"
                        :on-remove="handleRemove"
                        :file-list="fileList"
                        :headers="headerUrl"
                        :on-success="handleAvatarSuccess"
                        :data="imgDetails"
                        list-type="picture"
                    >
                        <el-button class="defbtn">点击上传</el-button>
                        <template #tip >
                            <div class="el-upload__tip">
                                <p>PC端推荐1920*480(4:1)</p>
                                <p>只能上传jpg/png文件，且不超过10M</p>
                                
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
                <el-form-item label="跳转方式">
                    <el-radio-group v-model="form.isJump">
                        <el-radio :value="0">无跳转</el-radio>
                        <el-radio :value="1">链接</el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <el-form-item v-if="form.isJump == 1" label="详情链接" prop="detailLink">
                    <el-input v-model="form.detailLink" placeholder="请输入详情链接" clearable></el-input>
                </el-form-item>
            </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button class="linebtn" @click="close">取 消</el-button>
                <el-button class="defbtn40" @click="onSubmit">确定</el-button>
            </div>
        </template>
        
    </el-dialog>
</template>

<script setup>
import { 
    getMyBookList, 
    saveMaterial, 
    getMaterialDetail, 
    updateMaterial, 
    deleteMaterial } from '@/api/expert/index.js'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { onMounted, reactive } from 'vue';
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headerUrl = ref({
  Authorization: localStorage.getItem('token') 
})
var fileList = ref([])
const imgDetails = ref({ serviceName: 'web' })
const dialogTitle = ref('新增轮播图')
const levelMap = ref({
    1: '本科',
    2: '高职'
})
let form = reactive({
    id: '',
    name: '',
    url:'',
    type:'',
    isJump:0,
    detailLink: '',
})
const rules = ref({
    name: [
        { required: true, message: '请输入教材名称', trigger: 'blur' }
    ],
    coverUrl: [
        { required: true, message: '请上传教材封面', trigger: 'blur' }
    ],
    detailLink: [
        { 
            pattern: /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{1,64})?\.)+[a-z]{2,6}\/?/, 
            message: '请输入正确的链接格式', 
            trigger: 'blur' 
        }
    ]
})
const emits = defineEmits(['reload'])
const specialData = ref()
const addform = ref()
const show = () =>{
    dialogVisible.value = true;
}
function resetForm() {
    fileList.value = [];
    for (let key in form) {
        form[key] = '';
    }
}
function close() {
    addform.value.resetFields();
    resetForm()
    dialogVisible.value = false
}
function onSubmit() {
    addform.value.validate((valid) => {
        if (valid) {
            const request = form.id ? 
                updateMaterial(form) : 
                saveMaterial(form);
            
            request.then(res => {
                if (res.status == 0) {
                    ElMessage.success(this.form.id ? '更新成功' : '保存成功');
                    dialogVisible.value = false;
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return false;
        }
    });
}
function handleRemove(file, fileList) {
    fileList.value = fileList;
    form.coverUrl = '';
    form.coverName = '';
    form.coverSize = '';
}
function handlePreview(file) {
    
}
function handleAvatarSuccess(res, file, fileList) {
    fileList.value = fileList;
    if (res.status == 0) {
        form.coverUrl = res.data.url;
        form.coverName = res.data.fileName;
        form.coverSize = res.data.size;
    } else {
        ElMessage.error('上传失败：' + res.msg);
    }
}

onMounted(()=>{

})
defineExpose({
    show
})
</script>

<style lang="scss" scoped>
.upload-demo {
  .el-upload {
    width: 100%;
  }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

</style>

<style lang="scss">

</style>