<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">奖励与荣誉</div>
      <el-button class="addbtns" v-if="isShow" size="small" @click="showAdd"
        >添加奖励与荣誉</el-button
      >
    </div>
    <div class="listdiv">
      <el-card class="carditem" v-for="(item, index) in listData" :key="index">
        <template #header>
          <span>{{ item.name }}</span>
          <el-dropdown
            v-if="isShow"
            class="right"
            @command="(e) => commandAction(e, item)"
          >
            <span class="bclass">
              更多操作<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <template #dropdow>
              <el-dropdown-menu >
                <el-dropdown-item command="2">编辑</el-dropdown-item>
                <el-dropdown-item command="3">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
            
          </el-dropdown>
        </template>
        <div class="">
          <div class="rdiv">
            <div class="rname">
              获奖类型：<span>{{ item.awardType }}</span>
            </div>
            <div class="rname">
              获奖时间：<span>{{ item.awardTime }}</span>
            </div>

            <div class="rname">
              <span class="linedis"> 获奖证书：</span>
              <img class="coverimg" :src="item.fileUrl" alt="" />
            </div>
            <div class="rname">
              内容介绍：
              <span class="neirong">
                <el-tooltip
                  popper-class="widthcss"
                  class="itemspan"
                  effect="dark"
                  :content="item.awardIntro"
                  placement="top"
                >
                  <span>{{ item.awardIntro }}</span>
                </el-tooltip>
              </span>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="adddialog"
      width="493px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close"
    >
      <el-form
        :rules="rules"
        ref="myform"
        :model="formInline"
        label-width="100px"
      >
        <el-form-item label="获奖名称：" prop="name">
          <el-input
            v-model="formInline.name"
            placeholder="请输入获奖名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="获奖证书：" prop="fileUrl">
          <el-upload
            class="upload-demo"
            :action="getUrl"
            :headers="headers"
            :data="fileData"
            :accept="'.jpeg,.jpg,png,.JPEG,.JPG,.PNG'"
            :on-success="handleSuccess"
            :on-remove="removeHandle"
            multiple
            :limit="1"
            :file-list="fileList"
          >
            <el-button class="defbtn" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="获奖类型：" prop="awardType">
          <el-input
            placeholder="请输入获奖类型"
            v-model="formInline.awardType"
          ></el-input>
        </el-form-item>
        <el-form-item label="获奖时间：" prop="awardTime">
          <el-date-picker
            v-model="formInline.awardTime"
            class="wid100"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="选择获奖日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="内容介绍：">
          <el-input
            v-model="formInline.awardIntro"
            placeholder="请输入内容介绍"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="btns">
          <el-button class="linebtn" @click="close">取 消</el-button>
          <el-button class="defbtn40" @click="submitAction">确 定</el-button>
        </div>
        
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref } from "vue";
import {
  saveAward,
  getListAllAward,
  getAwardinfo,
  updateAward,
  deleteAward,
} from '@/api/center/awardhonor'
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
const route = useRoute()
const getUrl = computed(()=>{
  return import.meta.env.VITE_GLOB_BASE_URL + '/aliyun/oss/uploadFiles'
})
const dialogVisible = ref(false)
const headers = ref({
  Authorization: localStorage.getItem('token') 
})
console.log('token',headers);
var fileList = ref([])
const fileData = ref({ serviceName: 'web' })
const pageBean = reactive({
  pageNum: 1,
  pageSize: 4,
})
const total = ref(0)
var dialogFormVisible = ref(false)
const dTitle = ref('添加获奖与荣誉')
let formInline = reactive({
  name: '',
  awardTime: '',
  fileUrl: '',
  fileSize: '',
  fileName: '',
  awardType: '',
  awardIntro: '',
})
const listData = ref([])
const rules = ref({
  awardType: [
    { required: true, message: '请输入获奖类型', trigger: 'blur' },
  ],
  fileUrl: [
    { required: true, message: '请上传获奖证书', trigger: 'change' },
  ],
  awardTime: [
    {
      required: true,
      message: '请选择日期',
      trigger: 'change',
    },
  ],
  name: [{ required: true, message: '请输入获奖名称', trigger: 'blur' }],
})
const editId = ref('')
const isShow = ref(false)
function commandAction(idx, data) {
  editId.value = data.id
  if (idx == 2) {
    dialogFormVisible.value = true
    getAwardinfoApi()
    return
  }
  if (idx == 3) {
    return deleteAction(data)
  }
}
function removeHandle() {
  formInline.fileUrl = ''
  formInline.fileName = ''
  formInline.fileSize = ''
}
function getAwardinfoApi() {
  getAwardinfo(this.editId).then((res) => {
    formInline = res.data
    fileList.value = []
    if (res.data.fileUrl) {
      fileList.value.push({
        url: res.data.fileUrl,
        name: res.data.fileName,
      })
    }
  })
}
function loaddata() {
  getListAllAward(pageBean)
  .then((result) => {
    listData.value = result.data
    total.value = result.page.total
  })
  .catch((err) => {})
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  editId.value = ''
  dialogFormVisible.value = true
  let obj = formInline
  Object.keys(obj).forEach((key) => {
    obj[key] = ''
  })
}
function deleteAction(data) {
  this.$confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      deleteAward({ id: data.id }).then((res) => {
        ElMessage({
          type: 'success',
          message: '删除成功!',
        })
        loaddata()
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      })
    })
}
function handleSuccess(res, file) {
  if (res.data.url) {
    formInline.fileName = res.data.fileName
    formInline.fileSize = res.data.size
    formInline.fileUrl = res.data.url
  } else {
    ElMessage({
      type:'error',
      message:'上传失败'
    })
  }
}
function close() {
  dialogFormVisible.value = false
}
function update() {
  delete formInline.createTime
  updateAward(formInline)
    .then((result) => {
      if (result.data) {
        ElMessage.success('更新成功')
        loaddata()
        dialogFormVisible.value = false
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function add() {
  saveAward(formInline)
    .then((result) => {
      if (result.data) {
        ElMessage.success('保存成功')
        loaddata()
        dialogFormVisible.value = false
      } else {
        ElMessage.error(result.msg)
      }
    })
    .catch((err) => {})
}
function submitAction(){
  const myform = ref('myform')
  myform.validate((valid) => {
    if (valid) {
      if (editId.value) {
        update()
      } else {
        add()
      }
    } else {
      return false
    }
  })
}
onMounted(()=>{
    pageBean.createBy = route.query.id
    if (localStorage.getItem('id') == route.query.id) {
      isShow.value = true
    }
    loaddata()
})
defineExpose({
  loaddata
})
</script>

<style>
.widthcss {
  width: 600px;
}
</style>

<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
}
.itemspan {
  width: 311px;
  display: block;
}
.neirong {
  display: block;
  width: calc(100% - 65px);
  float: right;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.linedis {
  display: inline-block;
  vertical-align: top;
}
.delbtn {
  color: #f56c6c;
  cursor: pointer;
}
.bclass {
  color: #386cfc;
}
.mydialog{
  ::v-deep(.el-dialog__body) {
    overflow: auto;
    
  }
  .el-input{
    ::v-deep(.el-input__inner){
      padding:0px 10px !important;
    }
    
  }
  
}
::v-deep(.el-input__prefix){
  padding-left: 10px !important;
}
.tcontcss {
  font-weight: 400;
  font-size: 12px;
  color: #636663;
  line-height: 20px;
  margin-top: 12px;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #386CFC;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right:10px;
  background-color: #386CFC;
}
.rname {
  width: 100%;
  margin-left: 8px;
  line-height: 20px;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #999999;
}
.rname span {
  color: #666666;
}
.rdiv {
}
.flex {
  display: flex;
  width: 100%;
}
.coverimg {
  width: 150px;
  aspect-ratio: 216/122 !important;
}
.carditem {
  width: calc(50% - 10px);
  margin-right: 16px;
  margin-bottom: 20px;
}
.carditem:nth-child(2n) {
  margin-right: 0px;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:hover {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:focus {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
</style>
