<template>
  <div class="bgcss">
    <div class="headcss">
      <div class="ticon">论文与会议报告</div>
      <el-button v-if="isShow" class="addbtns" size="small" @click="showAdd"
        >添加论文与会议报告</el-button
      >
    </div>
    <div class="listdiv">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="名称"> </el-table-column>
        <el-table-column prop="intro" label="简介" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="type" label="类型">
          <template #default="scope">
            {{ scope.row.type == 1 ? '论文' : '会议报告' }}
          </template>
        </el-table-column>

        <el-table-column prop="reportTime" label="发布时间" width="180">
        </el-table-column>
        <el-table-column prop="edit" label="操作" width="100" v-if="isShow">
          <template #default="scope">
            <el-button size="small" type="text" @click="editAction(scope.row)"
              >编辑</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button
              size="small"
              type="text"
              class="delbtn"
              @click="deleteAction(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="bottomcss">
      <el-pagination
        :current-page="pageBean.pageNum"
        :page-size="pageBean.pageSize"
        :total="total"
        @current-change="changePage"
        background
      ></el-pagination>
    </div>
    <el-dialog
      class="adddialog"
      width="493px"
      v-model="dialogFormVisible"
      :title="dTitle"
      @close="close2"
    >
      <el-form label-width="100px" :model="form" :rules="rules" ref="myform">
        <el-form-item label="名称：" prop="name">
          <el-input placeholder="请输入名称" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="简介：" prop="intro">
          <el-input
            type="textarea"
            placeholder="请输入简介"
            :rows="6"
            v-model="form.intro"
          ></el-input>
        </el-form-item>
        <el-form-item label="发布时间：" prop="reportTime">
          <el-date-picker
            v-model="form.reportTime"
            type="date"
            class="wid100"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发布类型：" prop="type">
          <el-select v-model="form.type" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="btns">
          <el-button class="linebtn" @click="close2">取 消</el-button>
          <el-button class="defbtn40" type="primary" @click="submitAction">保 存</el-button>
        </div>
        
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  thesisList,
  thesisSave,
  thesisDelete,
  thesisInfo,
  thesisEdit,
} from '@/api/center/thesis'
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useRoute,useRouter } from 'vue-router';
const route = useRoute()
const router = useRouter()
const isShow = ref(false)
const dialogFormVisible = ref(false)
const isEdit = ref(false)
const editId = ref(null)
const dTitle = ref('')
const pageBean = reactive({
    pageNum: 1,
    pageSize: 4,
    type: 1,
})
const tableData = ref([])
const total = ref(0)
const options = ref([
  {
    label: '论文',
    value: 1,
  },
  {
    label: '会议报告',
    value: 2,
  },
])
let form = reactive({
  name: '',
  type: '',
  reportTime: '',
  intro: '',
})
const rules = ref({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  intro: [{ required: true, message: '请输入简介', trigger: 'blur' }],
  type: [
    { required: true, message: '请选择发布类型', trigger: 'change' },
  ],
  reportTime: [
    { required: true, message: '请选择申报时间', trigger: 'change' },
  ],
})
function loaddata() {
  thesisList(pageBean).then((result) => {
    tableData.value = result.data
    total.value = result.page.total
  })
}
function changePage(page) {
  pageBean.pageNum = page
  loaddata()
}
function showAdd() {
  editId.value = ''
  dialogFormVisible.value = true
  dTitle.value = '添加论文与会议报告'
  let obj = form
  Object.keys(obj).forEach((key) => {
    obj[key] = ''
  })
}
function editAction(row) {
  dialogFormVisible.value = true
  dTitle.value = '编辑论文与会议报告'
  isEdit.value = true
  editId.value = row.id
  getDetailInfo(row.id)
}
function getDetailInfo(id) {
  thesisInfo(id).then((result) => {
    if (result.data) {
      form = result.data
    } else {
      ElMessage.error('获取详情失败')
    }
  })
}
const myform = ref()
function close2() {
  dialogFormVisible.value = false
  nextTick(() => {
    myform.value.resetFields()
  })
}
function deleteAction(row) {
  ElMessageBox.confirm('此操作将删除当前数据, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      thesisDelete({ id: row.id }).then((result) => {
        if (result.data) {
          ElMessage({
            type: 'success',
            message: '删除成功!',
          })
          loaddata()
        } else {
          ElMessage.error(result.msg)
        }
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除',
      })
    })
}
function submitAction() {
  myform.value.validate((valid) => {
    if (valid) {
      if (form.id) {
        delete form.createTime
      }
      const action = form.id ? thesisEdit : thesisSave
      action(form).then((result) => {
        if (result.data) {
          ElMessage.success(form.id ? '编辑成功' : '保存成功')
          loaddata()
          close2()
        } else {
          ElMessage.error(result.msg)
        }
      })
    } else {
      return false
    }
  })
}
onMounted(()=>{
  pageBean.createBy = route.query.id
    if (localStorage.getItem('id') == route.query.id) {
      isShow.value = true
    }
    loaddata()
})
</script>
<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
}
.delbtn {
  color: #f56c6c;
  cursor: pointer;
}
.ticon {
  font-size: 14px;
  font-weight: 500;
  color: #386CFC;
  line-height: 40px;
  padding-left: 10px;
}
.ticon::before {
  content: '|';
  right: 10px;
  background-color: #386CFC;
}
.bottomcss {
  margin-top: 20px;
}
.bgcss {
  padding: 20px 0px !important;
}
.listdiv {
  margin-top: 20px;
}
.headcss {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: right;
}
.addbtns {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:hover {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
.addbtns:focus {
  background-color: #386CFC;
  color: white;
  border: none !important;
}
</style>