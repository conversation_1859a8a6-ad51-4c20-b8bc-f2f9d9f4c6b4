<template>
    <div ref="editorRef" style="z-index: 999;"></div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import E from 'wangeditor';
 
const editorRef = ref(null);
let editor = null;
const getContentHtml = computed(() => editor.txt.html())
const getContentText = computed(() => editor.txt.html())
const setHtml = (text) =>{
    editor.txt.html(text)
}
const initEditor = () =>{
    editor = new E(editorRef.value);
    editor.customConfig = editor.customConfig ? editor.customConfig : editor.config
    editor.customConfig.uploadImgShowBase64 = false; // base64存储图片（推荐）
    editor.customConfig.uploadImgServer = `${import.meta.env.VITE_GLOB_BASE_URL}/aliyun/oss/uploadFiles`; // 配置服务器端地址（不推荐）
    editor.customConfig.uploadImgHeaders = { }; // 自定义header
    editor.customConfig.uploadFileName = "file"; // 后端接受上传文件的参数名
    editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为（默认最大支持2M）
    editor.customConfig.uploadImgMaxLength = 6; // 限制一次最多上传6张图片
    editor.customConfig.uploadImgTimeout = 1 * 60 * 1000; // 设置超时时间（默认1分钟）
    // 配置菜单
    editor.customConfig.menus = [
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "justify", // 对齐方式
        "quote", // 引用
        "image", // 插入图片
        "undo", // 撤销
        "redo", // 重复
        "fullscreen" // 全屏
    ];
    editor.customConfig.uploadImgHooks = {
        fail: (xhr, editor, result) => {
            // 插入图片失败回调
        },
        success: (xhr, editor, result) => {
            // 图片上传成功回调
        },
        timeout: (xhr, editor) => {
            // 网络超时的回调
        },
        error: (xhr, editor) => {
            // 图片上传错误的回调
        },
        customInsert: (insertImg, result, editor) => {
            console.log("url===>",result.data);
            // 图片上传成功，插入图片的回调（不推荐）
            insertImg(result.data.url);
        }
    };
    editor.customConfig.onchange = html => {
        
    };
    // 创建富文本编辑器
    editor.create();
}
onMounted(() => {
    initEditor()
});
onUnmounted(() => {
  if (editor) {
    editor.destroy(); // 销毁编辑器实例
    editor = null; // 置空引用，防止内存泄漏
  }
});
defineExpose({
    getContentHtml,
    getContentText,
    setHtml
})
</script>

<style lang="scss" scoped>
::v-deep(.w-e-text-container){
    border: 1px solid #E1E4EB !important;
    border-top: 0 !important;
    border-radius: 0px 0px 8px 8px;
}
::v-deep(.w-e-toolbar){
    border: 1px solid #E1E4EB;
}
</style>