<template>
  <el-dialog
    v-model="dialogVisible"
    width="493px"
    top="30vh"
    class="msgdialog"
    :title="tname"
    @close="close"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
  >
    <img class="msgicon" :src="imgUrl[messageType]" alt="" />
    <div class="msgcss">{{ message }}</div>
    <div class="btns" v-if="messageType == 'edit'">
        <el-button class="linebtn" @click="close">{{leftText}}</el-button>
        <el-button class="defbtn40" @click="onSubmit">{{rightText}}</el-button>
      </div>
  </el-dialog>
</template>

<script setup>
import { reactive } from "vue"
import infoImg from '@/assets/errorimg.png';
import successimg from '@/assets/successimg.png';
import rejectImg from '@/assets/reject.png';

const dialogVisible = ref(false)
const messageType = ref('success')
const tname = ref('')
const message = ref('')
const leftText = ref('取消')
const rightText = ref('确定')
const imgUrl = reactive({
    success:successimg,
    error:infoImg,
    reject:rejectImg,
    edit:infoImg
})
const closeAction = ref(null)
const submit = ref(null)
function show({ type, title, msg, onClose,leftBtnText,submitBtnText,submitAction }) {
    messageType.value = type
    tname.value = title
    message.value = msg
    leftText.value  = leftBtnText ? leftBtnText : '取消'
    rightText.value = submitBtnText ? submitBtnText : '确认'
    closeAction.value = onClose
    submit.value = submitAction
    dialogVisible.value = true
}
function close() {
    dialogVisible.value = false
    if (closeAction.value) {
        closeAction.value()
    }
}
function onSubmit(){
  dialogVisible.value = false
  if (submit.value){
    submit.value()
  }
}
defineExpose({
  show,
})
</script>
<style lang="scss">
.msgdialog {
  border-radius: 8px !important;
  overflow: hidden;
  .el-dialog__header{
      padding: 24px 30px;
      background: linear-gradient( 180deg, #E7EDFE 0%, rgba(231,237,254,0) 100%) !important;
  }
  .el-dialog__body{
    text-align: center !important;
    padding-bottom: 40px !important;
    min-height: 250px !important;
  }
    .el-dialog__headerbtn{
        width: 24px !important;
        height: 24px !important;
        font-size: 20px !important;
        right: 30px;
        top: 24px;
        .el-icon{
            width: 24px;
            height: 24px;
            color: #2D2F33 !important;
        }
    }
    
}
</style>
<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  .el-button +.el-button {
    margin-right: 16px;
  }
}
.msgicon {
  width: 62px;
  height: 62px;
  margin: 0 auto !important;
}
.titlename {
  margin-top: 16px;
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.msgcss {
  margin-top: 24px;
  height: 27px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 18px;
  color: #44474D;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
