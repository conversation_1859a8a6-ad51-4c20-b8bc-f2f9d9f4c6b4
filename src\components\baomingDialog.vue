<template>
  <el-dialog
    v-model="dialog.visible"
    :width="width"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="close"
    class="nd-dialog-box"
    v-if="dialog.visible"
    :show-close="false"
    :style="{ height: height }"
  >
    <template #header="{ close }">
      <div class="title">{{ title }}</div>
      <img class="close" src="../assets/close.png" alt="" @click="close" />
    </template>
    <div class="dialog-content-box">
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-form-item label="姓名" prop="pass">
          <el-input
            v-model="ruleForm.pass"
            type="password"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="checkPass">
          <el-input
            v-model="ruleForm.checkPass"
            type="password"
            autocomplete="off"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="showFooter" class="dialog-footer-box">
      <div class="dialog-footer">
        <el-button type="primary" class="submit">立即报名</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import { onMounted, ref, inject, reactive, nextTick } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: '教研活动预约',
  },
  showFooter: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: '340px',
  },
  height: {
    type: String,
    default: '372px',
  },
})
const dialog = reactive({
  visible: false,
  loading: false,
})
// 打开
const open = () => {
  dialog.visible = true
}
// 关闭
const close = () => {
  dialog.visible = false
}
defineExpose({
  open,
  close,
})

const ruleForm = reactive({})
const rules = reactive({})
</script>
<style lang="scss">
.nd-dialog-box {
  padding: 0px;
  border-radius: 8px;
  .el-dialog__header {
    position: relative;
    height: 71px;
    line-height: 71px;
    background: linear-gradient(
      180deg,
      #e7edfe 0%,
      rgba(231, 237, 254, 0) 100%
    );
    padding: 0 !important;
    padding-left: 30px !important;
  }

  .el-dialog__body {
    padding: 0 30px;
  }
}
</style>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  height: 40px;
}
:deep(.el-form-item__label) {
  height: 40px;
  line-height: 40px;
  margin-bottom: 0px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2d2f33;
}
.submit {
  width: 100%;
  height: 42px;
  background: #386cfc;
  border-radius: 4px 4px 4px 4px;
  margin-top: 22px;
}
.submit:hover {
  background: #386cfc;
}
.close {
  top: 24px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  right: 24px;
}
.title {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
}

.dialog-content-box {
  width: auto;
  height: auto;
  .dialog-content {
    width: auto;
    overflow-y: auto;
  }
}

.dialog-footer-box {
  width: 100%;
  height: 60px;
  .dialog-footer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
}
</style>