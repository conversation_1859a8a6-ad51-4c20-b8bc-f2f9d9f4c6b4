<template>
    <el-dialog
        :title="'回复：' + replyName"
        v-model="dialogVisible"
        width="50%"
        @close="closeDialog"
        class="mydialog"
        :close-on-click-modal="false"
    >
        <el-form :model="form" ref="formRef">
        <el-form-item label="评论回复:">
            <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入你的回复~"
            maxlength="400"
            show-word-limit
            v-model="form.content"
            ></el-input>
        </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="btns" type="primary" @click="submitComment">立即发布</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { reactive, ref, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { questionSave } from '@/api/study.js'

const props = defineProps({
    visible: Boolean,
    parentId: String,
    replyName: String,
})

const emit = defineEmits(['update:visible', 'refresh'])

const route = useRoute()
const formRef = ref()

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

let form = reactive({
    parentId: '',
    title: '',
    content: '',
    researchOfficeId: route.query.researchOfficeId,
    type: 2,
    isRecover: 1,
})

watch(() => props.parentId, (newVal) => {
    form.parentId = newVal
})

const closeDialog = () => {
    emit('update:visible', false)
    form.content = ''
}

const submitComment = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            questionSave(form)
                .then(response => {
                    ElMessage.success('回复成功')
                    emit('update:visible', false)
                    form.content = ''
                    emit('refresh')
                })
        } else {
            return false
        }
    })
}
</script>